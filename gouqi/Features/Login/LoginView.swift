import SwiftUI
import ComposableArchitecture
import LogCore

// 添加点击空白处收起键盘的功能扩展
extension View {
    func dismissKeyboardOnTap(validateAction: (() -> Void)? = nil) -> some View {
        modifier(DismissKeyboardOnTap(validateAction: validateAction))
    }
}

// 自定义修饰符，用于添加点击收起键盘功能
struct DismissKeyboardOnTap: ViewModifier {
    let validateAction: (() -> Void)?
    
    init(validateAction: (() -> Void)? = nil) {
        self.validateAction = validateAction
    }
    
    func body(content: Content) -> some View {
        content
            .contentShape(Rectangle()) // 确保整个区域可点击
            .onTapGesture {
                UIApplication.shared.sendAction(#selector(UIResponder.resignFirstResponder), to: nil, from: nil, for: nil)
                // 执行验证操作
                validateAction?()
            }
    }
}

// 邮箱验证工具
struct EmailValidator {
    // 严格的邮箱验证规则
    static func isValid(_ email: String) -> Bool {
        guard !email.isEmpty else { return false }
        
        // 基本格式验证: 使用正则表达式
        let emailRegex = "[A-Z0-9a-z._%+-]+@[A-Za-z0-9.-]+\\.[A-Za-z]{2,64}"
        let emailPredicate = NSPredicate(format: "SELF MATCHES %@", emailRegex)
        guard emailPredicate.evaluate(with: email) else { return false }
        
        // 其他验证规则
        let components = email.components(separatedBy: "@")
        guard components.count == 2 else { return false }
        
        let username = components[0]
        let domain = components[1]
        
        // 用户名长度检查
        guard username.count >= 3 else { return false }
        
        // 域名检查
        guard domain.contains(".") else { return false }
        
        return true
    }
    
    // 获取具体的错误信息
    static func getErrorMessage(for email: String) -> String {
        if email.isEmpty {
            return "請輸入電子郵件地址"
        }
        
        // 检查是否包含@符号
        if !email.contains("@") {
            return "電子郵件地址必須包含 @ 符號"
        }
        
        // 分割邮箱地址
        let components = email.components(separatedBy: "@")
        if components.count != 2 {
            return "電子郵件地址格式不正確"
        }
        
        let username = components[0]
        let domain = components[1]
        
        // 用户名长度检查
        if username.isEmpty {
            return "電子郵件地址必須包含用户名部分"
        }
        
        if username.count < 3 {
            return "用户名長度至少為3個字符"
        }
        
        // 域名检查
        if !domain.contains(".") {
            return "域名格式不正確，缺少頂級域名"
        }
        
        // 域名部分的其他检查
        let domainComponents = domain.components(separatedBy: ".")
        if domainComponents.last?.count ?? 0 < 2 {
            return "頂級域名格式不正確"
        }
        
        // 基本格式验证
        let emailRegex = "[A-Z0-9a-z._%+-]+@[A-Za-z0-9.-]+\\.[A-Za-z]{2,64}"
        let emailPredicate = NSPredicate(format: "SELF MATCHES %@", emailRegex)
        if !emailPredicate.evaluate(with: email) {
            return "電子郵件地址格式不正確，請檢查後重試"
        }
        
        return "電子郵件地址格式不正確"
    }
}

struct LoginView: View {
    let store: StoreOf<LoginFeature>
    
    init(store: StoreOf<LoginFeature>) {
        self.store = store
        XLog.d("LoginView initialized")
    }
    
    var body: some View {
        WithViewStore(store, observe: { $0 }) { viewStore in
            ZStack {
                // 背景色
                Color("color-background").edgesIgnoringSafeArea(.all)
                
                ScrollView {
                    VStack(spacing: 10) {
                        // 标题
                        Text("登入")
                            .font(.system(size: 56, weight: .semibold))
                            .primaryGradientForeground() // 使用渐变色而不是单色
                            .frame(maxWidth: .infinity, alignment: .leading)
                            .padding(.bottom, 16)
                        
                        // 邮箱输入框
                        emailTextField(viewStore)
                        
                        // 移除错误提示文本
                        
                        // 验证码输入框和发送按钮
                        codeInputView(viewStore).padding(.top,10)
                        
                       
                        
                        // 隐私政策提示
                        HStack(spacing: 0) {
                            Text("點擊登入表示你已閱讀並同意 ")
                                .foregroundColor(Color.gray)
                                .font(.system(size: 12))
                            Text("用戶協議")
                                .primaryGradientForeground()
                                .font(.system(size: 12))
                            Text(" 和 ")
                                .foregroundColor(Color.gray)
                                .font(.system(size: 12))
                            Text("隱私政策")
                                .primaryGradientForeground()
                                .font(.system(size: 12))
                            Text(" 。")
                                .foregroundColor(Color.gray)
                                .font(.system(size: 12))
                        }
                        .frame(maxWidth: .infinity, alignment: .leading)
                    
                        .fixedSize(horizontal: false, vertical: true)
                        .padding(.top,10)
                        
                        // 登录按钮
                        RoundedButton(
                            width: UIScreen.main.bounds.width - 48, // 左右各减去24的padding
                            height: 52,
                            cornerRadius: 8,
                            style: .linearPlan1,
                            action: {
                                XLog.i("Login button tapped")
                                Task {
                                    await viewStore.send(.loginButtonTapped).finish()
                                }
                            }
                        ) {
                            if viewStore.isLoading {
                                ProgressView()
                                    .progressViewStyle(CircularProgressViewStyle(tint: .white))
                            } else {
                                Text("登入")
                                    .foregroundColor(.white)
                                    .font(.system(size: 18, weight: .semibold))
                            }
                        }
                        .disabled(viewStore.isLoading)
                        .padding(.top,10)
                        
                        // 新用户提示文本
                        Text("新用戶可直接登入")
                            .font(.system(size: 14))
                            .foregroundColor(Color(white: 1, opacity: 0.64)) // #FFFFFFA3
                    
                        
                        
                        // 分隔线
                        HStack {
                            Rectangle()
                                .fill(Color.white)
                                .frame(height: 2)
                            Text("或 以第三方登入")
                                .foregroundColor(Color.white)
                                .font(.system(size: 16))
                                .padding(.horizontal, 16)
                                .lineLimit(1)
                                .fixedSize()
                            Rectangle()
                                .fill(Color.white)
                                .frame(height: 2)
                        }
                        .padding(.vertical, 10)
                        
                        // 第三方登录按钮
                        VStack(spacing: 16) {
                            // Google 登录
                            Button(action: { }) {
                                HStack {
                                    Image("google-original")
                                        .resizable()
                                        .frame(width: 38, height: 38)
                                        .foregroundColor(.white)
                                    Text("Google")
                                        .foregroundColor(.white)
                                        .font(.system(size: 16, weight: .medium))
                                }
                                .frame(maxWidth: .infinity)
                                .frame(height: 52)
                                .background(
                                    RoundedRectangle(cornerRadius: 8)
                                        .stroke(Color.gray, lineWidth: 1)
                                )
                            }
                            
                            // Apple ID 登录
                            Button(action: { }) {
                                HStack {
                                    Image("apple-original")
                                        .resizable()
                                        .aspectRatio(contentMode: .fit)
                                        .frame(width: 38, height: 38)
                                        .foregroundColor(.white)
                                    Text("Apple ID")
                                        .foregroundColor(.white)
                                        .font(.system(size: 16, weight: .medium))
                                }
                                .frame(maxWidth: .infinity)
                                .frame(height: 52)
                                .background(
                                    RoundedRectangle(cornerRadius: 8)
                                        .stroke(Color.gray, lineWidth: 1)
                                )
                            }
                        }.padding(.top,20)
                        
                    }
                    .padding(.horizontal, 24)
                    .padding(.top, 60) // 添加顶部间距
                    .padding(.bottom, 24)
                    .dismissKeyboardOnTap {
                        // 收起键盘时验证邮箱
                        if !viewStore.email.isEmpty {
                            Task {
                                await viewStore.send(.validateEmail).finish()
                            }
                        }
                    } // 添加点击空白处收起键盘功能并触发验证
                }
                
                // 重新添加Toast视图
                ToastView()
            }
            .onAppear {
                XLog.d("LoginView appeared")
            }
        }
    }
    
    // 邮箱输入框组件
    private func emailTextField(_ viewStore: ViewStoreOf<LoginFeature>) -> some View {
        VStack(spacing: 4) {
            ZStack(alignment: .leading) {
                if viewStore.email.isEmpty {
                    Text("電子郵件")
                        .foregroundColor(Color.gray)
                        .padding(.leading, 16)
                }
                
                TextField("", text: viewStore.binding(
                    get: \.email,
                    send: { .emailChanged($0) }
                ), onEditingChanged: { isEditing in
                    // 当用户结束输入时触发验证
                    if !isEditing && !viewStore.email.isEmpty {
                        Task {
                            await viewStore.send(.validateEmail).finish()
                        }
                    }
                })
                .keyboardType(.emailAddress)
                .autocapitalization(.none)
                .disableAutocorrection(true)
                .padding(.leading, 16)
                .foregroundColor(.white)
                .onSubmit {
                    // 在用户按下键盘完成按钮时验证
                    Task {
                        await viewStore.send(.validateEmail).finish()
                    }
                }
            }
            .padding([.top, .bottom, .trailing])
            .background(
                RoundedRectangle(cornerRadius: 8)
                    .fill(Color.white.opacity(0.24)) // 添加半透明白色背景 #FFFFFF3D
                    .overlay(
                        RoundedRectangle(cornerRadius: 8)
                            .stroke(Color.white.opacity(0.5), lineWidth: 1)
                    )
            )
        }
    }
    
    // 验证码输入框和发送按钮组件
    private func codeInputView(_ viewStore: ViewStoreOf<LoginFeature>) -> some View {
        HStack(spacing: 0) {
            // 验证码输入框部分
            ZStack(alignment: .leading) {
                if viewStore.verificationCode.isEmpty {
                    Text("驗證碼")
                        .foregroundColor(Color.gray)
                        .padding(.leading, 16)
                }
                
                TextField("", text: viewStore.binding(
                    get: \.verificationCode,
                    send: { .verificationCodeChanged($0) }
                ))
                .keyboardType(.numberPad)
                .padding(.leading, 16)
                .padding(.trailing, viewStore.verificationCode.isEmpty ? 16 : 40)
                .foregroundColor(.white)
                .onTapGesture {
                    // 移除点击验证码输入框时的邮箱验证
                }
                
                // 添加清除按钮
                if !viewStore.verificationCode.isEmpty {
                    Button(action: {
                        Task {
                            await viewStore.send(.verificationCodeChanged("")).finish()
                        }
                    }) {
                        Circle()
                            .fill(Color.white.opacity(0.3))
                            .frame(width: 20, height: 20)
                            .overlay(
                                Image(systemName: "xmark")
                                    .font(.system(size: 10))
                                    .foregroundColor(.white)
                            )
                    }
                    .frame(width: 20, height: 20)
                    .padding(.trailing, 16)
                    .frame(maxWidth: .infinity, alignment: .trailing)
                }
            }
            .frame(height: 56)
            .frame(maxWidth: .infinity)
            .background(Color.white.opacity(0.24)) // 添加半透明白色背景 #FFFFFF3D
            
            // 发送验证码按钮
            Button(action: {
                // 点击发送验证码时再次验证邮箱
                Task {
                    await viewStore.send(.validateEmail).finish()
                    // 如果邮箱有效,发送验证码
                    if viewStore.isEmailValid {
                        await viewStore.send(.sendVerificationCode).finish()
                    }
                }
            }) {
                if viewStore.sendCodeInProgress {
                    ProgressView()
                        .progressViewStyle(CircularProgressViewStyle(tint: .white))
                        .frame(width: 120, height: 56)
                } else if viewStore.countdownSeconds > 0 {
                    Text("\(viewStore.countdownSeconds)秒后重试")
                        .font(.system(size: 14))
                        .foregroundColor(.gray)
                        .frame(width: 120, height: 56)
                } else {
                    Text("獲取驗證碼")
                        .font(.system(size: 14))
                        .primaryGradientForeground()
                        .frame(width: 120, height: 56)
                }
            }
            .disabled(viewStore.countdownSeconds > 0 || viewStore.sendCodeInProgress || !viewStore.isEmailValid)
            .background(Color.white.opacity(0.24)) // 添加半透明白色背景 #FFFFFF3D
        }
        .background(
            RoundedRectangle(cornerRadius: 8)
                .stroke(Color.white.opacity(0.5), lineWidth: 1)
        )
        .clipShape(RoundedRectangle(cornerRadius: 8)) // 确保内容不超出边框圆角
    }
}

#Preview {
    // 为预览创建模拟Store
    let store = Store(initialState: LoginFeature.State()) {
        LoginFeature()
    }
    
    return LoginView(store: store)
        .preferredColorScheme(.dark) // 确保使用深色模式预览
}

// 添加包含各种状态的预览
struct LoginView_Previews: PreviewProvider {
    static var previews: some View {
        Group {
            // 常规状态
            LoginView(
                store: Store(initialState: LoginFeature.State()) {
                    LoginFeature()
                }
            )
            .previewDisplayName("常规状态")
            
            // 显示错误信息
            LoginView(
                store: Store(initialState: LoginFeature.State(
                    email: "invalid-email",
                    isEmailValid: false,
                    errorMessage: "请输入有效的邮箱地址"
                )) {
                    LoginFeature()
                }
            )
            .previewDisplayName("错误状态")
            
            // 正在加载状态
            LoginView(
                store: Store(initialState: LoginFeature.State(
                    email: "<EMAIL>",
                    verificationCode: "123456",
                    isEmailValid: true,
                    isLoading: true
                )) {
                    LoginFeature()
                }
            )
            .previewDisplayName("加载状态")
            
            // 验证码倒计时状态
            LoginView(
                store: Store(initialState: LoginFeature.State(
                    email: "<EMAIL>", 
                    isEmailValid: true,
                    countdownSeconds: 45
                )) {
                    LoginFeature()
                }
            )
            .previewDisplayName("倒计时状态")
            
            // 验证码已输入状态（显示X按钮）
            LoginView(
                store: Store(initialState: LoginFeature.State(
                    email: "<EMAIL>",
                    verificationCode: "123456",
                    isEmailValid: true
                )) {
                    LoginFeature()
                }
            )
            .previewDisplayName("验证码已输入状态")
        }
        .preferredColorScheme(.dark) // 确保所有预览都使用深色模式
    }
} 
