import SwiftUI
import ComposableArchitecture
import LogCore

public struct LaunchView: View {
    let store: StoreOf<LaunchFeature>
  
    public var body: some View {
        WithViewStore(self.store, observe: { $0 }) { viewStore in
            AppBaseView(backgroundColor: Color.colorBackground) {
                HStack(spacing: 10) {
                    Image("icon")
                        .resizable()
                        .aspectRatio(contentMode: .fit)
                        .frame(width: 80, height: 80)
                    Image("icon_text")
                        .resizable()
                        .aspectRatio(92.0/56.0, contentMode: .fit)
                        .frame(height: 80)
                }
            }
            .onAppear {
                Task {
                    await viewStore.send(.onAppear).finish()
                }
            }
        }
    }
} 
